import 'package:flutter_test/flutter_test.dart';
import 'package:ecoplug/models/station.dart';
import 'package:ecoplug/models/evse.dart';
import 'package:ecoplug/features/stations/application/stations_provider.dart'
    as provider;
import 'package:ecoplug/features/station/application/station_list_notifier.dart'
    as notifier;
import 'package:ecoplug/features/station/domain/station_state.dart';

void main() {
  group('Station List Unmodifiable List Tests', () {
    test('StationsState should create mutable lists', () {
      // Test StationsState from stations_provider.dart
      final state1 = provider.StationsState();
      expect(state1.stations, isA<List<Station>>());
      expect(state1.stations.length, 0);

      // Should be able to add stations without error
      final newStations = [
        _createTestStation(id: '1', uid: 'uid1', name: 'Station 1'),
        _createTestStation(id: '2', uid: 'uid2', name: 'Station 2'),
      ];

      final updatedState = state1.copyWith(stations: newStations);
      expect(updatedState.stations.length, 2);
      expect(updatedState.stations[0].name, 'Station 1');
    });

    test('StationsNotifier StationsState should create mutable lists', () {
      // Test StationsState from station_list_notifier.dart
      final state2 = notifier.StationsState();
      expect(state2.stations, isA<List<Station>>());
      expect(state2.stations.length, 0);

      // Should be able to add stations without error
      final newStations = [
        _createTestStation(id: '3', uid: 'uid3', name: 'Station 3'),
      ];

      final updatedState = state2.copyWith(stations: newStations);
      expect(updatedState.stations.length, 1);
      expect(updatedState.stations[0].name, 'Station 3');
    });

    test('StationState should create mutable lists', () {
      // Test StationState from station_state.dart
      final state3 = StationState();
      expect(state3.stations, isA<List<Station>>());
      expect(state3.stations.length, 0);

      // Should be able to add stations without error
      final newStations = [
        _createTestStation(id: '4', uid: 'uid4', name: 'Station 4'),
        _createTestStation(id: '5', uid: 'uid5', name: 'Station 5'),
        _createTestStation(id: '6', uid: 'uid6', name: 'Station 6'),
      ];

      final updatedState = state3.copyWith(stations: newStations);
      expect(updatedState.stations.length, 3);
      expect(updatedState.stations[1].name, 'Station 5');
    });

    test('Factory constructors should work correctly', () {
      // Test factory constructors
      final initialState = StationState.initial();
      expect(initialState.stations.length, 0);
      expect(initialState.isLoading, false);

      final loadingState = StationState.loading();
      expect(loadingState.isLoading, true);
      expect(loadingState.stations.length, 0);

      final testStations = [
        _createTestStation(id: '7', uid: 'uid7', name: 'Station 7'),
      ];
      final loadedState = StationState.loaded(testStations);
      expect(loadedState.stations.length, 1);
      expect(loadedState.stations[0].name, 'Station 7');
    });

    test('List operations should not throw unmodifiable list errors', () {
      // Test that we can perform list operations without errors
      final state = provider.StationsState();

      // Simulate adding stations like in the real app
      final station1 =
          _createTestStation(id: '8', uid: 'uid8', name: 'Station 8');
      final station2 =
          _createTestStation(id: '9', uid: 'uid9', name: 'Station 9');

      // First update
      final state1 = state.copyWith(stations: [station1]);
      expect(state1.stations.length, 1);

      // Second update (pagination simulation)
      final state2 = state1.copyWith(stations: [...state1.stations, station2]);
      expect(state2.stations.length, 2);
      expect(state2.stations[0].name, 'Station 8');
      expect(state2.stations[1].name, 'Station 9');

      // Clear stations (search simulation)
      final state3 = state2.copyWith(stations: <Station>[]);
      expect(state3.stations.length, 0);
    });
  });
}

// Helper function to create test stations without default values
Station _createTestStation({
  required String id,
  required String uid,
  required String name,
  String? address,
  String? city,
  String? state,
  List<String>? images,
  List<Evse>? evses,
  double? latitude,
  double? longitude,
  double? distance,
  String? status,
  double? rating,
  int? reviews,
  List<Connector>? connectors,
}) {
  return Station(
    id: id,
    uid: uid,
    name: name,
    address: address ?? '',
    city: city,
    state: state,
    images: images ?? [],
    evses: evses ?? [],
    latitude: latitude ?? 0.0,
    longitude: longitude ?? 0.0,
    distance: distance ?? 0.0,
    status: status ?? 'Unknown',
    rating: rating ?? 0.0,
    reviews: reviews ?? 0,
    connectors: connectors ?? [],
  );
}

// DELETED: copyWith extension - using helper function instead
