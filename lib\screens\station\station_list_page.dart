import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:ecoplug/models/station.dart';
import 'package:ecoplug/core/api/api_service.dart';
import 'package:ecoplug/services/api_bridge.dart';
import 'package:ecoplug/screens/station/station_details_page.dart';
import 'package:ecoplug/services/location_service.dart';
import '../../utils/dark_mode_wrapper.dart';
import 'dart:async'; // Import Timer

class StationListPage extends StatefulWidget {
  const StationListPage({super.key});

  @override
  StationListPageState createState() => StationListPageState();
}

class StationListPageState extends State<StationListPage> {
  // Current filter: 'All', 'Available', or 'Unavailable'.
  String _selectedFilter = 'All';

  // Current search text.
  String _searchText = '';

  // API service instance
  final ApiService _apiService = ApiService();
  final ApiBridge _apiBridge = ApiBridge();
  final LocationService _locationService = LocationService();

  // Authentication state
  bool _isAuthenticated = false;

  // List of stations
  List<Station> _stations = [];
  bool _isLoading = true;
  bool _isLoadingMore = false; // Track loading more stations
  bool _hasMore = true; // Track if more stations are available
  int _currentPage = 1;
  final int _limit = 20; // Number of stations per page
  String? _errorMessage;

  // User location for API calls
  double? _userLatitude;
  double? _userLongitude;

  // Scroll controller for pagination
  final ScrollController _scrollController = ScrollController();

  // Debouncer for search input
  Timer? _debounce;

  @override
  void initState() {
    super.initState();
    _initializeLocationAndLoadStations();

    // Add listener for scroll controller
    _scrollController.addListener(() {
      // Check if scrolled to the bottom and not already loading more
      if (_scrollController.position.pixels >=
              _scrollController.position.maxScrollExtent - 200 &&
          !_isLoadingMore &&
          _hasMore &&
          _searchText.isEmpty) {
        // Only paginate when not searching
        _loadStations();
      }
    });
  }

  /// Initialize user location and load stations
  Future<void> _initializeLocationAndLoadStations() async {
    debugPrint('🌍 StationListPage: Starting location initialization...');
    try {
      // Get user location first
      final position = await _locationService.getCurrentLocation();
      if (position != null) {
        setState(() {
          _userLatitude = position.latitude;
          _userLongitude = position.longitude;
        });
        debugPrint(
            '🌍 StationListPage: Location obtained successfully - lat: $_userLatitude, lng: $_userLongitude');
      } else {
        debugPrint(
            '🌍 StationListPage: Location service returned null position');
      }
    } catch (e) {
      debugPrint('🌍 StationListPage: Error getting user location: $e');
    }

    // Debug log final location state before loading stations
    debugPrint(
        '🌍 StationListPage: Final location state - lat: $_userLatitude, lng: $_userLongitude');

    // Load stations regardless of location success
    _loadStations(isInitialLoad: true);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _debounce?.cancel();
    super.dispose();
  }

  Future<void> _loadStations(
      {bool isInitialLoad = false, bool isSearch = false}) async {
    if (_isLoadingMore || (isSearch && _isLoading)) {
      return; // Prevent multiple simultaneous loads
    }

    setState(() {
      if (isInitialLoad || isSearch) {
        _isLoading = true;
        _stations.clear(); // Clear stations for initial load or new search
        _currentPage = 1;
        _hasMore = true;
      } else {
        _isLoadingMore = true;
      }
      _errorMessage = null;
    });

    try {
      List<Station> newStations;
      if (_searchText.isNotEmpty) {
        // Use API search if search text is present
        final searchResponse =
            await _apiService.searchStationsByName(_searchText);
        if (searchResponse.success && searchResponse.data != null) {
          newStations = searchResponse.data!.map((searchStation) {
            // Create connectors from types
            final connectors = searchStation.types
                    ?.map((type) => Connector(
                          id: '${searchStation.uid ?? searchStation.stationId}_${type.name}',
                          name: type.name ?? 'Connector type not specified',
                          type: type.name ?? '',
                          power: '',
                          availableGuns: 1,
                          status: searchStation.status ?? 'Available',
                          icon: type.icon,
                        ))
                    .toList() ??
                [
                  Connector(
                    id: '${searchStation.uid ?? searchStation.stationId}_default',
                    name: 'Standard Connector',
                    type: 'Standard',
                    power: '',
                    availableGuns: 1,
                    status: 'Available',
                  )
                ];

            return Station(
              id: (searchStation.uid?.isNotEmpty == true)
                  ? searchStation.uid!
                  : searchStation.stationId?.toString() ?? '',
              name: searchStation.name ?? 'Station name not specified',
              address: searchStation.address ?? 'Address not specified',
              latitude: searchStation.latitude ?? 0.0,
              longitude: searchStation.longitude ?? 0.0,
              distance: 0.0, // Search results don't include distance
              status: searchStation.status ?? '',
              rating: 0.0,
              reviews: 0,
              images: [],
              evses: [],
              connectors: connectors,
              uid: searchStation.uid,
              city: searchStation.city,
              state: null,
              types: searchStation.types?.map((t) => t.toJson()).toList(),
            );
          }).toList();
        } else {
          newStations = [];
        }
        _hasMore = false; // API search doesn't support pagination
      } else {
        // Debug log the coordinates being passed to API
        debugPrint(
            '🚀 StationListPage: Making API call with coordinates - lat: $_userLatitude, lng: $_userLongitude');
        debugPrint('🚀 StationListPage: Page: $_currentPage, Limit: $_limit');

        // Fetch paginated stations using ApiBridge with user location
        final paginatedResponse = await _apiBridge.getPaginatedStations(
          _currentPage,
          _limit,
          latitude: _userLatitude,
          longitude: _userLongitude,
        );

        debugPrint(
            '🚀 StationListPage: API response received - Success: ${paginatedResponse.success}');
        debugPrint(
            '🚀 StationListPage: API response message: ${paginatedResponse.message}');
        debugPrint(
            '🚀 StationListPage: API response data length: ${paginatedResponse.data?.length ?? 0}');

        // The getPaginatedStations returns ApiResponse<List<Station>>
        if (paginatedResponse.success && paginatedResponse.data != null) {
          final fetchedStations = paginatedResponse.data!;

          // Debug log distance values received in UI
          debugPrint(
              '🎯 StationListPage: Received ${fetchedStations.length} stations');
          for (int i = 0; i < fetchedStations.length && i < 3; i++) {
            final station = fetchedStations[i];
            debugPrint(
                '🎯 StationListPage: Station ${i + 1} - ${station.name}, Distance: ${station.distance}');
          }

          if (isInitialLoad || isSearch) {
            newStations = fetchedStations;
          } else {
            newStations = [..._stations, ...fetchedStations];
          }

          // For now, assume there are more pages if we got a full page
          _hasMore = fetchedStations.length >= _limit;
        } else {
          newStations = isInitialLoad ? [] : _stations;
          _hasMore = false;
        }
      }

      setState(() {
        // If it wasn't an initial load or search, the service handles appending.
        // We just need to update our local state reference.
        _stations = newStations;
        if (!isInitialLoad && !isSearch) {
          _currentPage++;
        }
      });
    } catch (e) {
      debugPrint('🚨 StationListPage: Exception in _loadStations: $e');
      debugPrint('🚨 StationListPage: Exception type: ${e.runtimeType}');
      setState(() {
        _errorMessage = "Failed to load stations: $e";
        _hasMore = false; // Stop pagination on error
      });
    } finally {
      setState(() {
        _isLoading = false;
        _isLoadingMore = false;
      });
    }
  }

  // Debounced search function
  void _onSearchChanged(String value) {
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    _debounce = Timer(const Duration(milliseconds: 500), () {
      if (_searchText != value) {
        // Only trigger search if text actually changed
        setState(() {
          _searchText = value;
        });
        _loadStations(isSearch: true); // Trigger API search
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    // Filter the station list locally ONLY by the availability filter
    // Search is handled by the API call in _loadStations
    List<Station> filteredStations = _stations;
    if (_selectedFilter != 'All') {
      final isAvailable = _selectedFilter.toLowerCase() == 'available';
      filteredStations = filteredStations.where((station) {
        // Use the isAvailable getter from the Station model
        return station.isAvailable == isAvailable;
      }).toList();
    }

    // Sorting is still done locally after fetching/filtering
    filteredStations.sort((a, b) => a.distance.compareTo(b.distance));

    return DarkModeWrapper(
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Station List'),
          backgroundColor: Theme.of(context)
              .colorScheme
              .surface, // Use theme-adaptive surface color
          foregroundColor: Theme.of(context)
              .colorScheme
              .onSurface, // Use theme-adaptive text color
          elevation: 0,
          leading: IconButton(
            icon: Icon(
              Icons.arrow_back,
              color: Theme.of(context)
                  .colorScheme
                  .onSurface, // Use theme-adaptive icon color
            ),
            onPressed: () => Navigator.pop(context),
          ),
        ),
        backgroundColor: Theme.of(context)
            .colorScheme
            .surface, // Use theme-adaptive surface color
        body: Column(
          children: [
            // Completely redesigned search bar with no extra layers
            Padding(
              padding:
                  const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
              child: TextField(
                onTap: () {},
                onSubmitted: (_) {},
                onChanged: _onSearchChanged,
                style: TextStyle(
                  color: Theme.of(context)
                      .colorScheme
                      .onSurface, // Use theme-adaptive text color
                  fontSize: 15,
                ),
                decoration: InputDecoration(
                  hintText: 'Search charger, city...',
                  hintStyle: TextStyle(
                    color: Theme.of(context)
                        .colorScheme
                        .onSurface
                        .withAlpha(153), // 0.6 * 255 = 153
                    fontSize: 15,
                  ),
                  prefixIcon: Icon(
                    Icons.search,
                    color: Theme.of(context)
                        .colorScheme
                        .onSurface
                        .withAlpha(179), // 0.7 * 255 = 179
                    size: 20,
                  ),
                  filled: true,
                  fillColor: Theme.of(context)
                      .colorScheme
                      .surfaceContainerHighest, // Use theme-adaptive fill color
                  contentPadding:
                      const EdgeInsets.symmetric(vertical: 15, horizontal: 16),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide.none,
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide.none,
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide.none,
                  ),
                ),
              ),
            ),

            // Filter buttons row.
            _buildFilterButtons(),

            // Station list.
            Expanded(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : _errorMessage != null
                      ? Center(child: Text(_errorMessage!))
                      : filteredStations.isEmpty
                          ? const Center(child: Text('No stations found'))
                          : ListView.builder(
                              controller:
                                  _scrollController, // Assign scroll controller
                              itemCount: filteredStations.length +
                                  (_isLoadingMore
                                      ? 1
                                      : 0), // Add space for loader
                              itemBuilder: (context, index) {
                                if (index == filteredStations.length) {
                                  // If it's the last item and we're loading more, show indicator
                                  return _buildLoadingIndicator();
                                }
                                final station = filteredStations[index];
                                return _buildStationCard(station);
                              },
                            ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build the row of filter buttons.
  Widget _buildFilterButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        _buildFilterButton('All'),
        _buildFilterButton('AVAILABLE'),
        _buildFilterButton('UNAVAILABLE'),
      ],
    );
  }

  /// Build an individual filter button.
  Widget _buildFilterButton(String label) {
    final isSelected = (_selectedFilter == label);

    return ElevatedButton(
      style: ElevatedButton.styleFrom(
        backgroundColor: isSelected
            ? Theme.of(context)
                .colorScheme
                .primary // Use theme primary color for selected
            : Theme.of(context)
                .colorScheme
                .surfaceContainerHighest, // Use theme surface container for unselected
        foregroundColor: isSelected
            ? Theme.of(context)
                .colorScheme
                .onPrimary // Use theme on-primary color for selected text
            : Theme.of(context)
                .colorScheme
                .onSurface, // Use theme on-surface color for unselected text
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
          side: BorderSide.none, // CRITICAL FIX: Remove all borders/outlines
        ),
        elevation: isSelected ? 2 : 0, // Subtle elevation for selected state
        shadowColor: isSelected
            ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.3)
            : null,
      ),
      onPressed: () => setState(() => _selectedFilter = label),
      child: Text(
        label,
        style: TextStyle(
          fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    return const Padding(
      padding: EdgeInsets.all(16.0),
      child: Center(
        child: CircularProgressIndicator(),
      ),
    );
  }

  /// Extract connector types text from station API data
  String _getConnectorTypesText(Station station) {
    // First try to get connector types from the types field (direct API data)
    if (station.types != null && station.types!.isNotEmpty) {
      Set<String> connectorNames = {};

      for (var type in station.types!) {
        if (type is Map<String, dynamic> && type['name'] != null) {
          connectorNames.add(type['name'].toString());
        } else if (type is Map && type['name'] != null) {
          connectorNames.add(type['name'].toString());
        }
      }

      if (connectorNames.isNotEmpty) {
        return connectorNames.join(', ');
      }
    }

    // Fallback to connectors field if types is not available
    if (station.connectors.isNotEmpty) {
      return station.connectors
          .map((c) => c.type)
          .where((type) => type.isNotEmpty)
          .toSet()
          .join(', ');
    }

    // Default fallback
    return 'Various';
  }

  Widget _buildStationCard(Station station) {
    final colorScheme = Theme.of(context).colorScheme;

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      elevation: 2,
      color: colorScheme.surface, // Use theme surface color
      child: InkWell(
        onTap: () {
          // Navigate to station details with proper UID validation
          final stationUid = station.uid ?? station.id;
          if (stationUid.isNotEmpty) {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => StationDetailsPage(
                  uid: stationUid,
                  station: station,
                ),
              ),
            );
          } else {
            // Show error if no valid UID
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Station ID not available'),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          station.name,
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                            color:
                                colorScheme.onSurface, // Use theme text color
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          station.address,
                          style: TextStyle(
                            color: colorScheme
                                .onSurfaceVariant, // Use theme secondary text color
                            fontSize: 14,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: station.isAvailable
                          ? const Color(0xFF34C759)
                              .withAlpha(26) // 0.1 * 255 = ~26
                          : Colors.red.withAlpha(26), // 0.1 * 255 = ~26
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: station.isAvailable
                            ? const Color(0xFF34C759)
                                .withAlpha(77) // 0.3 * 255 = ~77
                            : Colors.red.withAlpha(77), // 0.3 * 255 = ~77
                        width: 0.5,
                      ),
                    ),
                    child: Text(
                      station.isAvailable ? 'Available' : 'In Use',
                      style: TextStyle(
                        color: station.isAvailable
                            ? const Color(0xFF34C759)
                            : Colors.red,
                        fontWeight: FontWeight.w600,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: const Color(0xFF3D7AF5)
                              .withAlpha(26), // 0.1 * 255 = ~26
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: const Icon(
                          Icons.bolt,
                          color: Color(0xFF3D7AF5),
                          size: 16,
                        ),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        // Use connector types directly from API data
                        _getConnectorTypesText(station),
                        style: const TextStyle(
                          color: Color(0xFF3D7AF5),
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                        maxLines:
                            1, // Prevent long lists from wrapping excessively
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                  // Conditional distance display - only show if distance > 0
                  Builder(
                    builder: (context) {
                      // Debug log distance value for each station
                      debugPrint(
                          '🎯 StationCard: ${station.name} - Distance: ${station.distance}');

                      if (station.distance > 0) {
                        debugPrint(
                            '🎯 StationCard: Showing distance UI for ${station.name}');
                        return Row(
                          children: [
                            Icon(
                              Icons.location_on,
                              color: colorScheme
                                  .onSurfaceVariant, // Use theme icon color
                              size: 16,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              '${station.distance.toStringAsFixed(2)} km',
                              style: TextStyle(
                                color: colorScheme
                                    .onSurfaceVariant, // Use theme secondary text color
                                fontSize: 14,
                              ),
                            ),
                          ],
                        );
                      } else {
                        debugPrint(
                            '🎯 StationCard: Hiding distance UI for ${station.name} (distance = ${station.distance})');
                        return const SizedBox.shrink();
                      }
                    },
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    flex: 1,
                    child: ElevatedButton.icon(
                      style: ElevatedButton.styleFrom(
                        backgroundColor: station.isAvailable
                            ? const Color(
                                0xFF34C759) // Keep the green color for charging
                            : colorScheme
                                .surfaceContainerHighest, // Use theme color for disabled
                        foregroundColor: station.isAvailable
                            ? Colors.white
                            : colorScheme.onSurfaceVariant,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        minimumSize: const Size.fromHeight(45),
                        elevation: station.isAvailable ? 2 : 0,
                      ),
                      onPressed: station.isAvailable
                          ? () {
                              // Navigate to station details with proper UID validation
                              final stationUid = station.uid ?? station.id;
                              if (stationUid.isNotEmpty) {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => StationDetailsPage(
                                      uid: stationUid,
                                      station: station,
                                    ),
                                  ),
                                );
                              } else {
                                // Show error if no valid UID
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text('Station ID not available'),
                                    backgroundColor: Colors.red,
                                  ),
                                );
                              }
                            }
                          : null,
                      icon: const Icon(Icons.power,
                          size: 18, color: Colors.white),
                      label: const Text(
                        'Start Charging',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 13,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    flex: 1,
                    child: ElevatedButton.icon(
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors
                            .blue, // Fixed: Use blue color for directions button
                        foregroundColor:
                            Colors.white, // White text on blue background
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        minimumSize: const Size.fromHeight(45),
                        elevation: 2,
                      ),
                      onPressed: () async {
                        final uri = Uri.parse(
                            'https://www.google.com/maps/dir/?api=1&destination=${station.latitude},${station.longitude}');
                        try {
                          if (await canLaunchUrl(uri)) {
                            await launchUrl(uri,
                                mode: LaunchMode.externalApplication);
                          } else {
                            if (mounted) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text('Could not open directions'),
                                ),
                              );
                            }
                          }
                        } catch (e) {
                          if (mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text('Error: $e'),
                              ),
                            );
                          }
                        }
                      },
                      icon: const Icon(Icons.directions, size: 18),
                      label: const Text(
                        'Directions',
                        style: TextStyle(
                          fontSize: 13,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
