import 'package:flutter/foundation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:ecoplug/config/notification_config.dart';
import 'package:ecoplug/services/unified_notification_service.dart';

/// Centralized Notification Manager
/// Provides high-level notification operations using centralized configuration
class NotificationManager {
  static final NotificationManager _instance = NotificationManager._internal();
  factory NotificationManager() => _instance;
  NotificationManager._internal();

  final UnifiedNotificationService _unifiedService = UnifiedNotificationService();
  bool _isInitialized = false;

  /// Initialize the notification manager
  Future<void> initialize() async {
    if (_isInitialized) {
      debugPrint('🔔 Notification Manager already initialized');
      return;
    }

    try {
      debugPrint('🔔 ===== INITIALIZING NOTIFICATION MANAGER =====');

      await _unifiedService.initialize();

      _isInitialized = true;
      debugPrint('✅ Notification Manager initialized successfully');
    } catch (e) {
      debugPrint('❌ Error initializing Notification Manager: $e');
      rethrow;
    }
  }

  /// Show notification by type using centralized configuration
  Future<void> showNotificationByType(
    String notificationType, {
    Map<String, dynamic>? data,
    String? customTitle,
    String? customBody,
  }) async {
    try {
      final typeConfig = NotificationConfig.getNotificationType(notificationType);
      if (typeConfig == null) {
        debugPrint('❌ Notification type configuration not found: $notificationType');
        return;
      }

      final title = customTitle ?? typeConfig.title;
      final body = customBody ?? _formatBody(typeConfig.defaultBody, data);

      switch (notificationType) {
        case 'charging_started':
        case 'charging_progress':
        case 'charging_complete':
          await _showChargingNotification(typeConfig, title, body, data);
          break;

        case 'station_available':
        case 'trip_reminder':
        case 'payment_success':
        case 'low_balance':
        case 'promotion_offer':
          await _showGeneralNotification(typeConfig, title, body, data);
          break;

        case 'welcome_login':
          await _showWelcomeNotification(typeConfig, title, body, data);
          break;

        default:
          debugPrint('❌ Unsupported notification type: $notificationType');
      }
    } catch (e) {
      debugPrint('❌ Error showing notification: $e');
    }
  }

  /// Show charging notification with progress
  Future<void> _showChargingNotification(
    NotificationTypeConfig typeConfig,
    String title,
    String body,
    Map<String, dynamic>? data,
  ) async {
    final chargePercentage = (data?['charge_percentage'] as num?)?.toDouble() ?? 0.0;
    final isCharging = data?['is_charging'] as bool? ?? false;
    final stationName = data?['station_name'] as String?;
    final connectorType = data?['connector_type'] as String?;
    final powerKw = (data?['power_kw'] as num?)?.toDouble();
    final energyKwh = (data?['energy_kwh'] as num?)?.toDouble();
    final durationMinutes = data?['duration_minutes'] as int?;
    final cost = (data?['cost'] as num?)?.toDouble();

    await _unifiedService.showChargingNotification(
      title: title,
      body: body,
      chargePercentage: chargePercentage,
      isCharging: isCharging,
      stationName: stationName,
      connectorType: connectorType,
      powerKw: powerKw,
      energyKwh: energyKwh,
      duration: durationMinutes != null ? Duration(minutes: durationMinutes) : null,
      cost: cost,
    );
  }

  /// Show welcome notification
  Future<void> _showWelcomeNotification(
    NotificationTypeConfig typeConfig,
    String title,
    String body,
    Map<String, dynamic>? data,
  ) async {
    final userName = data?['user_name'] as String?;
    final isFirstLogin = data?['is_first_login'] as bool? ?? false;

    await _unifiedService.showWelcomeNotification(
      userName: userName,
      isFirstLogin: isFirstLogin,
    );
  }

  /// Show general notification
  Future<void> _showGeneralNotification(
    NotificationTypeConfig typeConfig,
    String title,
    String body,
    Map<String, dynamic>? data,
  ) async {
    // For now, use the test notification method
    // In a full implementation, you would create specific notification methods
    await _unifiedService.showTestNotification();
  }

  /// Format notification body with data placeholders
  String _formatBody(String template, Map<String, dynamic>? data) {
    if (data == null) return template;

    String formatted = template;
    data.forEach((key, value) {
      formatted = formatted.replaceAll('{$key}', value.toString());
    });
    return formatted;
  }

  /// Subscribe user to default topics based on preferences
  Future<void> subscribeUserToDefaultTopics(String userId, Map<String, bool> preferences) async {
    try {
      // Subscribe to user-specific topic
      await _unifiedService.subscribeToTopic('user_$userId');

      // Subscribe to topics based on preferences
      for (final entry in preferences.entries) {
        if (entry.value) {
          final topics = NotificationConfig.getTopicsForPreference(entry.key);
          for (final topic in topics) {
            await _unifiedService.subscribeToTopic(topic);
          }
        }
      }

      debugPrint('✅ User subscribed to default topics');
    } catch (e) {
      debugPrint('❌ Error subscribing user to topics: $e');
    }
  }

  /// Update user notification preferences
  Future<void> updateUserPreferences(Map<String, bool> preferences) async {
    try {
      await _unifiedService.updateNotificationPreferences(
        chargingUpdates: preferences['charging_updates'],
        stationAlerts: preferences['station_alerts'],
        promotions: preferences['promotions'],
        tripReminders: preferences['trip_reminders'],
      );

      debugPrint('✅ User preferences updated');
    } catch (e) {
      debugPrint('❌ Error updating user preferences: $e');
    }
  }

  /// Subscribe to location-based topics
  Future<void> subscribeToLocationTopics(String city, String state) async {
    await _unifiedService.subscribeToLocationTopics(city, state);
  }

  /// Get FCM token
  Future<String?> getFCMToken() async {
    return await _unifiedService.getFCMToken();
  }

  /// Send FCM token to backend
  Future<void> sendTokenToBackend(String userId) async {
    await _unifiedService.sendTokenToBackend(userId);
  }

  /// Check if notifications are enabled
  Future<bool> areNotificationsEnabled() async {
    return await _unifiedService.areNotificationsEnabled();
  }

  /// Get service status
  Map<String, bool> getServiceStatus() {
    final status = _unifiedService.getServiceStatus();
    status['notification_manager'] = _isInitialized;
    return status;
  }

  /// Get all available notification types
  List<NotificationTypeConfig> getAvailableNotificationTypes() {
    return NotificationConfig.notificationTypes.values.toList();
  }

  /// Get all available notification channels
  List<NotificationChannelConfig> getAvailableChannels() {
    return NotificationConfig.channels.values.toList();
  }

  /// Get all available topics
  List<TopicConfig> getAvailableTopics() {
    return NotificationConfig.topics.values.toList();
  }

  /// Get user configurable preferences
  List<NotificationPreference> getUserPreferences() {
    return NotificationConfig.getUserConfigurablePreferences();
  }

  /// Clear all notifications
  Future<void> clearAllNotifications() async {
    await _unifiedService.clearChargingNotification();
  }

  /// Show welcome notification after successful login
  Future<void> showWelcomeNotification({
    String? userName,
    bool isFirstLogin = false,
  }) async {
    await _unifiedService.showWelcomeNotification(
      userName: userName,
      isFirstLogin: isFirstLogin,
    );
  }

  /// Clear welcome notification
  Future<void> clearWelcomeNotification() async {
    await _unifiedService.clearWelcomeNotification();
  }

  /// Get welcome notification statistics
  Future<Map<String, dynamic>> getWelcomeStats() async {
    return await _unifiedService.getWelcomeStats();
  }

  /// Reset welcome notification tracking (debug mode only)
  Future<void> resetWelcomeTracking() async {
    if (kDebugMode) {
      await _unifiedService.resetWelcomeTracking();
    }
  }

  /// Show test notification
  Future<void> showTestNotification() async {
    if (kDebugMode) {
      await _unifiedService.showTestNotification();
    }
  }

  /// Unsubscribe user from all topics (logout)
  Future<void> unsubscribeUserFromAllTopics(String userId) async {
    await _unifiedService.unsubscribeFromUserTopics(userId);
  }

  /// Get notification configuration summary
  Map<String, dynamic> getConfigurationSummary() {
    return {
      'channels': NotificationConfig.channels.length,
      'topics': NotificationConfig.topics.length,
      'notification_types': NotificationConfig.notificationTypes.length,
      'user_preferences': NotificationConfig.getUserConfigurablePreferences().length,
      'default_topics': NotificationConfig.getDefaultTopics().length,
    };
  }

  /// Validate notification configuration
  bool validateConfiguration() {
    try {
      // Check if all required configurations exist
      final requiredChannels = ['charging_session', 'fcm_messages'];
      final requiredTopics = ['general_notifications', 'charging_updates'];
      final requiredTypes = ['charging_started', 'charging_complete'];

      for (final channel in requiredChannels) {
        if (NotificationConfig.getChannelConfig(channel) == null) {
          debugPrint('❌ Missing required channel: $channel');
          return false;
        }
      }

      for (final topic in requiredTopics) {
        if (NotificationConfig.getTopicConfig(topic) == null) {
          debugPrint('❌ Missing required topic: $topic');
          return false;
        }
      }

      for (final type in requiredTypes) {
        if (NotificationConfig.getNotificationType(type) == null) {
          debugPrint('❌ Missing required notification type: $type');
          return false;
        }
      }

      debugPrint('✅ Notification configuration validation passed');
      return true;
    } catch (e) {
      debugPrint('❌ Notification configuration validation failed: $e');
      return false;
    }
  }

  /// Dispose resources
  void dispose() {
    _unifiedService.dispose();
    debugPrint('🔔 Notification Manager disposed');
  }
}
