import 'package:flutter_test/flutter_test.dart';
import 'package:ecoplug/repositories/station_repository.dart';
import 'package:ecoplug/services/api_bridge.dart';
import 'package:ecoplug/services/location_service.dart';

void main() {
  group('Location and API Debug Tests', () {
    test('Test location service and station paginate API flow', () async {
      try {
        // Test location service first
        print('🔍 Testing LocationService...');
        final locationService = LocationService();
        final position = await locationService.getCurrentLocation();

        double testLatitude = 27.608162;
        double testLongitude = 76.613853;

        if (position != null) {
          testLatitude = position.latitude;
          testLongitude = position.longitude;
          print(
              '🔍 LocationService returned: lat=$testLatitude, lng=$testLongitude');
        } else {
          print('🔍 LocationService returned null, using default coordinates');
        }

        // Initialize repository (it creates its own ApiService internally)
        final stationRepository = StationRepository();

        print('🔍 Testing station paginate API with location parameters...');
        print('🔍 Test coordinates: lat=$testLatitude, lng=$testLongitude');

        // Test the repository method directly
        final response = await stationRepository.getPaginatedStations(
          1, // page
          20, // limit
          latitude: testLatitude,
          longitude: testLongitude,
        );

        print('🔍 Repository response success: ${response.success}');
        print('🔍 Repository response message: ${response.message}');

        if (response.success && response.data != null) {
          final stationsData = response.data!;
          print(
              '🔍 Number of stations returned: ${stationsData.data?.length ?? 0}');

          // Check first few stations for distance values
          if (stationsData.data != null && stationsData.data!.isNotEmpty) {
            for (int i = 0; i < stationsData.data!.length && i < 3; i++) {
              final station = stationsData.data![i];
              print(
                  '🔍 Station ${i + 1}: ${station.name} - Distance: ${station.distance}');
            }
          }
        } else {
          print('🔍 Repository call failed: ${response.message}');
        }

        // Test the ApiBridge method
        print('\n🔍 Testing ApiBridge method...');
        final apiBridge = ApiBridge();
        final bridgeResponse = await apiBridge.getPaginatedStations(
          1, // page
          20, // limit
          latitude: testLatitude,
          longitude: testLongitude,
        );

        print('🔍 ApiBridge response success: ${bridgeResponse.success}');
        print('🔍 ApiBridge response message: ${bridgeResponse.message}');

        if (bridgeResponse.success && bridgeResponse.data != null) {
          final stations = bridgeResponse.data!;
          print('🔍 Number of stations from ApiBridge: ${stations.length}');

          // Check first few stations for distance values
          for (int i = 0; i < stations.length && i < 3; i++) {
            final station = stations[i];
            print(
                '🔍 ApiBridge Station ${i + 1}: ${station.name} - Distance: ${station.distance}');
          }
        } else {
          print('🔍 ApiBridge call failed: ${bridgeResponse.message}');
        }

        // The test should pass if we get here without exceptions
        expect(true, isTrue);
      } catch (e) {
        print('🔍 Test failed with error: $e');
        fail('API test failed: $e');
      }
    });
  });
}
