import 'package:flutter_test/flutter_test.dart';
import 'package:ecoplug/repositories/station_repository.dart';
import 'package:ecoplug/services/api_bridge.dart';
import 'package:ecoplug/services/location_service.dart';
import 'package:ecoplug/models/station/paginated_stations_response.dart';

void main() {
  group('Location and API Debug Tests', () {
    test('Test JSON parsing with real API data', () async {
      // Test with the exact JSON data you provided
      final jsonData = {
        "longitude": 76.613811,
        "latitude": 27.607838,
        "station_id": 259,
        "uid": "be3d08f0-f34f-4e06-9bef-6a336747dd5f",
        "name": "Demo Ecoplug Office AC",
        "address": "Balla Boda, Kh. No. 147, Rath Nagar, Vijay Mandir Road,",
        "city": "Alwar",
        "distance": 0.03626404119316079,
        "types": [
          {
            "name": "SMARTPLUG",
            "icon": "https://api2.eeil.online/uploads/connector_type/splug1.svg"
          }
        ],
        "status": "Unavailable"
      };

      print('🔍 Testing JSON parsing with real API data...');
      print('🔍 Original distance value: ${jsonData['distance']}');
      print('🔍 Distance type: ${jsonData['distance'].runtimeType}');

      // Test PaginatedStation parsing
      final paginatedStation = PaginatedStation.fromJson(jsonData);
      print('🔍 PaginatedStation distance: ${paginatedStation.distance}');
      print(
          '🔍 PaginatedStation distance type: ${paginatedStation.distance.runtimeType}');

      // Test if distance > 0 condition
      final isDistanceGreaterThanZero = (paginatedStation.distance ?? 0.0) > 0;
      print('🔍 Is distance > 0: $isDistanceGreaterThanZero');

      // Test distance formatting
      final formattedDistance =
          (paginatedStation.distance ?? 0.0).toStringAsFixed(2);
      print('🔍 Formatted distance: ${formattedDistance} km');

      expect(paginatedStation.distance, isNotNull);
      expect(paginatedStation.distance! > 0, isTrue);
      expect(formattedDistance, equals('0.04'));
    });

    test('Test location service and station paginate API flow', () async {
      try {
        // Test location service first
        print('🔍 Testing LocationService...');
        final locationService = LocationService();
        final position = await locationService.getCurrentLocation();

        double testLatitude = 27.608162;
        double testLongitude = 76.613853;

        if (position != null) {
          testLatitude = position.latitude;
          testLongitude = position.longitude;
          print(
              '🔍 LocationService returned: lat=$testLatitude, lng=$testLongitude');
        } else {
          print('🔍 LocationService returned null, using default coordinates');
        }

        // Initialize repository (it creates its own ApiService internally)
        final stationRepository = StationRepository();

        print('🔍 Testing station paginate API with location parameters...');
        print('🔍 Test coordinates: lat=$testLatitude, lng=$testLongitude');

        // Test the repository method directly
        final response = await stationRepository.getPaginatedStations(
          1, // page
          20, // limit
          latitude: testLatitude,
          longitude: testLongitude,
        );

        print('🔍 Repository response success: ${response.success}');
        print('🔍 Repository response message: ${response.message}');

        if (response.success && response.data != null) {
          final stationsData = response.data!;
          print(
              '🔍 Number of stations returned: ${stationsData.data?.length ?? 0}');

          // Check first few stations for distance values
          if (stationsData.data != null && stationsData.data!.isNotEmpty) {
            for (int i = 0; i < stationsData.data!.length && i < 3; i++) {
              final station = stationsData.data![i];
              print(
                  '🔍 Station ${i + 1}: ${station.name} - Distance: ${station.distance}');
            }
          }
        } else {
          print('🔍 Repository call failed: ${response.message}');
        }

        // Test the ApiBridge method
        print('\n🔍 Testing ApiBridge method...');
        final apiBridge = ApiBridge();
        final bridgeResponse = await apiBridge.getPaginatedStations(
          1, // page
          20, // limit
          latitude: testLatitude,
          longitude: testLongitude,
        );

        print('🔍 ApiBridge response success: ${bridgeResponse.success}');
        print('🔍 ApiBridge response message: ${bridgeResponse.message}');

        if (bridgeResponse.success && bridgeResponse.data != null) {
          final stations = bridgeResponse.data!;
          print('🔍 Number of stations from ApiBridge: ${stations.length}');

          // Check first few stations for distance values
          for (int i = 0; i < stations.length && i < 3; i++) {
            final station = stations[i];
            print(
                '🔍 ApiBridge Station ${i + 1}: ${station.name} - Distance: ${station.distance}');
          }
        } else {
          print('🔍 ApiBridge call failed: ${bridgeResponse.message}');
        }

        // The test should pass if we get here without exceptions
        expect(true, isTrue);
      } catch (e) {
        print('🔍 Test failed with error: $e');
        fail('API test failed: $e');
      }
    });
  });
}
